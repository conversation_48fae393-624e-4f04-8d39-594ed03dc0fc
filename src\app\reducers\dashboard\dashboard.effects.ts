import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import { DashboardService } from 'src/app/services/controllers/dashboard.service';
import { CommonService } from 'src/app/services/shared/common.service';
import {
  dashboardActionTypes,
  FetchCPLTracking,
  FetchCPLTrackingSuccess,
  FetchGoogleAdsMarketingFinances,
  FetchGoogleAdsMarketingFinancesSuccess,
  FetchCustomStatusCount,
  FetchCustomStatusCountSuccess,
  FetchDashboardCallReport,
  FetchDashboardCallReportSuccess,
  FetchDashboardCalls,
  FetchDashboardCallsSuccess,
  FetchDashboardCountByStatus,
  FetchDashboardCountByStatusSuccess,
  FetchDashboardDataStatus,
  FetchDashboardDataStatusSuccess,
  FetchDashboardLeadReport,
  FetchDashboardLeadReportSuccess,
  FetchDashboardLeadsInContact,
  FetchDashboardLeadsInContactSuccess,
  FetchDashboardLeadSource,
  FetchDashboardLeadSourceSuccess,
  FetchDashboardLeadStatus,
  FetchDashboardLeadStatusSuccess,
  FetchDashboardLeadTracker,
  FetchDashboardLeadTrackerSuccess,
  FetchDashboardMeeting,
  FetchDashboardMeetingSuccess,
  FetchDashboardMeetingVisitCount,
  FetchDashboardMeetingVisitCountSuccess,
  FetchDashboardPerformance,
  FetchDashboardPerformanceSuccess,
  FetchDashboardPipeline,
  FetchDashboardPipelineSuccess,
  FetchDashboardReceived,
  FetchDashboardReceivedSuccess,
  FetchDashboardRemindersCountByDate,
  FetchDashboardRemindersCountByDateSuccess,
  FetchDashboardSiteVisit,
  FetchDashboardSiteVisitSuccess,
  FetchDashboardSource,
  FetchDashboardSourceData,
  FetchDashboardSourceDataSuccess,
  FetchDashboardSourceDetails,
  FetchDashboardSourceDetailsSuccess,
  FetchDashboardSourceSuccess,
  FetchDashboardUpcomingEvents,
  FetchDashboardUpcomingEventsSuccess,
  FetchDashboardWhatsapp,
  FetchDashboardWhatsappSuccess,
  FetchLeadStatusTotalCount,
  FetchLeadStatusTotalCountSuccess,
  FetchTopPerformer,
  FetchTopPerformerSuccess,
} from './dashboard.actions';
import { getFiltersPayloadV1 } from './dashboard.reducers';

@Injectable()
export class DashboardEffects {
  getDashboardCountByStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_COUNT_BY_STATUS),
      map((action: FetchDashboardCountByStatus) => action),
      switchMap((action: FetchDashboardCountByStatus) => {
        let payload: any = {
          ...action.payload,
          path: 'dashboard/countbystatus',
          fromDate: null,
          toDate: null,
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardCountByStatusSuccess(resp.data);
            }
            return new FetchDashboardCountByStatusSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardLeadReport$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_LEAD_REPORT),
      map((action: FetchDashboardLeadReport) => action),
      switchMap((action: FetchDashboardLeadReport) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/newleadreport',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardLeadReportSuccess(resp.data);
            }
            return new FetchDashboardLeadReportSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardVisitAndMeetingCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_VISIT_MEETING_COUNT),
      map((action: FetchDashboardMeetingVisitCount) => action),
      switchMap((action: FetchDashboardMeetingVisitCount) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/sitevisitandmeetingstatus',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardMeetingVisitCountSuccess(resp.data);
            }
            return new FetchDashboardMeetingVisitCountSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardUpcomingEvents$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_UPCOMING_EVENTS),
      map((action: FetchDashboardUpcomingEvents) => action),
      switchMap((action: FetchDashboardUpcomingEvents) => {
        let payload: any = {
          ...action.payload,
          path: 'dashboard/upcomingevents',
          fromDate: null,
          toDate: null,
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardUpcomingEventsSuccess(resp.data);
            }
            return new FetchDashboardUpcomingEventsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardCallReport$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_CALL_REPORT),
      map((action: FetchDashboardCallReport) => action),
      switchMap((action: FetchDashboardCallReport) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/callreport',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardCallReportSuccess(resp.data);
            }
            return new FetchDashboardCallReportSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardLeadTracker$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_LEAD_TRACKER),
      map((action: FetchDashboardLeadTracker) => action),
      switchMap((action: FetchDashboardLeadTracker) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/newleadtracker',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardLeadTrackerSuccess(resp.data);
            }
            return new FetchDashboardLeadTrackerSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardLeadsInContact$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_LEADS_IN_CONTACT),
      map((action: FetchDashboardLeadsInContact) => action),
      switchMap((action: FetchDashboardLeadsInContact) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/leadsincontactwith',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardLeadsInContactSuccess(resp.data);
            }
            return new FetchDashboardLeadsInContactSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardSourceData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_SOURCE_DATA),
      map((action: FetchDashboardSourceData) => action),
      switchMap((action: FetchDashboardSourceData) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/source',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardSourceDataSuccess(resp.data);
            }
            return new FetchDashboardSourceDataSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardLeadSource$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_LEAD_SOURCE),
      map((action: FetchDashboardLeadSource) => action),
      switchMap((action: FetchDashboardLeadSource) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/leadsource',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardLeadSourceSuccess(resp);
            }
            return new FetchDashboardLeadSourceSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardSourceDetails$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_SOURCE_DETAILS),
      map((action: FetchDashboardSourceDetails) => action),
      switchMap((action: FetchDashboardSourceDetails) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/sourcedetails',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            return new FetchDashboardSourceDetailsSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardPipeline$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_PIPELINE),
      map((action: FetchDashboardPipeline) => action),
      switchMap((action: FetchDashboardPipeline) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/leadspipeline',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardPipelineSuccess(resp.data);
            }
            return new FetchDashboardPipelineSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardReceived$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_RECEIVED),
      map((action: FetchDashboardReceived) => action),
      switchMap((action: FetchDashboardReceived) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/leadreceived',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardReceivedSuccess(resp.data);
            }
            return new FetchDashboardReceivedSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardRemindersCountByDate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_REMINDERS_COUNT_BY_DATE),
      map((action: FetchDashboardRemindersCountByDate) => action),
      switchMap((action: FetchDashboardRemindersCountByDate) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/upcomingevents/month',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardRemindersCountByDateSuccess(resp.data);
            }
            return new FetchDashboardRemindersCountByDateSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardTeamsPerformance$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_PERFORMANCE),
      map((action: FetchDashboardPerformance) => action),
      switchMap((action: FetchDashboardPerformance) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/teamsperformance/overview',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardPerformanceSuccess(resp.data);
            }
            return new FetchDashboardPerformanceSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardMeeting$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_MEETING),
      map((action: FetchDashboardMeeting) => action),
      switchMap((action: FetchDashboardMeeting) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/meetingdetails',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardMeetingSuccess(resp.data);
            }
            return new FetchDashboardMeetingSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardSiteVisit$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_SITE_VISIT),
      map((action: FetchDashboardSiteVisit) => action),
      switchMap((action: FetchDashboardSiteVisit) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/sitevisitdetails',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardSiteVisitSuccess(resp.data);
            }
            return new FetchDashboardSiteVisitSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getTopPerformer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_TOP_PERFORMER),
      map((action: FetchTopPerformer) => action),
      switchMap((action: FetchTopPerformer) => {
        let payload = {
          ...action.payload,
          path: 'dashboard/topthreeagents',
        };
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchTopPerformerSuccess(resp.items);
            }
            return new FetchTopPerformerSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCustomStatusCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_CUSTOM_STATUS_COUNT),
      map((action: FetchCustomStatusCount) => action),
      switchMap((action: FetchCustomStatusCount) => {
        let filterPayload: any = {};
        this.store.select(getFiltersPayloadV1).subscribe((data: any) => {
          filterPayload = data.global;
        });
        filterPayload = {
          ...filterPayload,
          Projects: null,
          path: 'dashboard/count/all/status',
        };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp) {
              return new FetchCustomStatusCountSuccess(resp);
            }
            return new FetchCustomStatusCountSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardSource$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_SOURCE),
      map((action: FetchDashboardSource) => action),
      switchMap((action: FetchDashboardSource) => {
        let filterPayload: any = {};
        this.store.select(getFiltersPayloadV1).subscribe((data: any) => {
          filterPayload = data.global;
        });
        filterPayload = { ...filterPayload, path: 'dashboard/leadsource/v1' };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardSourceSuccess(resp?.items);
            }
            return new FetchDashboardSourceSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardLeadStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_LEAD_STATUS),
      map((action: FetchDashboardLeadStatus) => action),
      switchMap((action: FetchDashboardLeadStatus) => {
        let filterPayload: any = {};
        this.store.select(getFiltersPayloadV1).subscribe((data: any) => {
          filterPayload = data.lead;
        });
        filterPayload = {
          ...filterPayload,
          path: 'dashboard/leadstatus/custom',
        };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardLeadStatusSuccess(resp.items);
            }
            return new FetchDashboardLeadStatusSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadStatusTotalCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_LEAD_STATUS_TOTAL_COUNT),
      map((action: FetchLeadStatusTotalCount) => action),
      switchMap((action: FetchLeadStatusTotalCount) => {
        let filterPayload: any = {};
        this.store.select(getFiltersPayloadV1).subscribe((data: any) => {
          filterPayload = data.lead;
        });
        filterPayload = {
          ...filterPayload,
          path: 'dashboard/lead/status/custom-count',
        };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp) {
              return new FetchLeadStatusTotalCountSuccess(resp);
            }
            return new FetchLeadStatusTotalCountSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardDataStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_DATA_STATUS),
      map((action: FetchDashboardDataStatus) => action),
      switchMap((action: FetchDashboardDataStatus) => {
        let filterPayload: any = {};
        this.store.select(getFiltersPayloadV1).subscribe((data: any) => {
          filterPayload = data.data;
        });
        filterPayload = { ...filterPayload, path: 'dashboard/datastatus' };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardDataStatusSuccess(resp);
            }
            return new FetchDashboardDataStatusSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardCalls$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_CALLS),
      map((action: FetchDashboardCalls) => action),
      switchMap((action: FetchDashboardCalls) => {
        let filterPayload: any = {};
        this.store.select(getFiltersPayloadV1).subscribe((data: any) => {
          filterPayload = data.call;
        });
        filterPayload = { ...filterPayload, path: 'dashboard/calls' };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardCallsSuccess(resp);
            }
            return new FetchDashboardCallsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDashboardWhatsapp$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_DASHBOARD_WHATSAPP),
      map((action: FetchDashboardWhatsapp) => action),
      switchMap((action: FetchDashboardWhatsapp) => {
        let filterPayload: any = {};
        this.store.select(getFiltersPayloadV1).subscribe((data: any) => {
          filterPayload = data.whatsapp;
        });
        filterPayload = { ...filterPayload, path: 'dashboard/whatsapp/chats' };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDashboardWhatsappSuccess(resp);
            }
            return new FetchDashboardWhatsappSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCplTracking$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_CPL_TRACKING),
      map((action: FetchCPLTracking) => action),
      switchMap((action: FetchCPLTracking) => {
        let filterPayload: any = {};
        this.store.select(getFiltersPayloadV1).subscribe((data: any) => {
          filterPayload = data.cpl;
        });
        const actionPayload = action.payload || {};
        filterPayload = {
          ...filterPayload,
          path: 'dashboard/user/marketingfinances',
          ...actionPayload
        };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchCPLTrackingSuccess(resp);
            }
            return new FetchCPLTrackingSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getGoogleAdsMarketingFinances$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dashboardActionTypes.FETCH_GOOGLE_ADS_MARKETING_FINANCES),
      map((action: FetchGoogleAdsMarketingFinances) => action),
      switchMap((action: FetchGoogleAdsMarketingFinances) => {
        let filterPayload: any = {};
        this.store.select(getFiltersPayloadV1).subscribe((data: any) => {
          filterPayload = data.googleAdsMarketing;
        });
        const actionPayload = action.payload || {};
        filterPayload = {
          ...filterPayload,
          path: 'dashboard/user/googleads/marketingfinances',
          ...actionPayload
        };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchGoogleAdsMarketingFinancesSuccess(resp);
            }
            return new FetchGoogleAdsMarketingFinancesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private api: DashboardService,
    private commonService: CommonService,
    private store: Store<AppState>
  ) { }
}

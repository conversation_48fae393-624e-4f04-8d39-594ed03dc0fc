import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { DATE_TYPE } from 'src/app/app.constants';
import { IntegrationSource, LeadDateType, LeadSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  generateLeadSourcesArray,
} from 'src/app/core/utils/common.util';
import {
  FetchProjectList,
  FetchPropertyList,
  FetchSubSourceList,
} from 'src/app/reducers/lead/lead.actions';
import {
  getProjectList,
  getProjectListIsLoading,
  getPropertyList,
  getPropertyListIsLoading,
  getSubSourceList,
  getSubSourceListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  getCustomStatusList,
  getCustomStatusListIsLoading,
} from 'src/app/reducers/status/status.reducer';
import { FetchReportees } from 'src/app/reducers/teams/teams.actions';
import {
  getReportees,
  getReporteesIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import {
  UpdateFilterPayload
} from 'src/app/reducers/whatsapp/whatsapp.actions';
import {
  getFiltersPayload,
  initialState as WAInitialFilterState,
} from 'src/app/reducers/whatsapp/whatsapp.reducer';

@Component({
  selector: 'whatsApp-inbox-adv-filters',
  templateUrl: './whatsApp-inbox-adv-filters.component.html',
})
export class WhatsAppInboxAdvFiltersComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  filtersPayload: any;
  showFilters: boolean;
  reportees: Array<Object>;
  reporteesIsLoading: boolean;
  isCustomStatusListLoading: any;
  customStatusList: any;
  canViewLeadSource: boolean;
  projectList: Array<string> = [];
  propertyList: Array<string> = [];
  allSubStatusList: any;
  subStatusList: any[];
  isCustomStatusEnabled: boolean = false;
  masterLeadStatus: Array<any> = JSON.parse(
    localStorage.getItem('masterleadstatus') || '[]'
  );
  leadSources: Array<string> = generateLeadSourcesArray();

  subSourceList: any;
  isProjectListLoading: boolean;
  allSubSourceList: any;
  subSourceListIsLoading: any;
  projectListIsLoading: boolean;
  propertyListIsLoading: boolean;
  dateTypeList: Array<string> = DATE_TYPE;
  maxDate: Date;
  advanceFilters: any;
  dateType: string;
  filterDate: any[];
  clearDataAndFilter: Function;
  // whatsAppData: any;

  constructor(
    private store: Store<AppState>,
    public modalService: BsModalService
  ) {
    this.store.dispatch(new FetchProjectList());
    this.store.dispatch(new FetchSubSourceList());
    this.store.dispatch(new FetchReportees());
    this.store.dispatch(new FetchPropertyList());
  }

  ngOnInit(): void {
    this.store
      .select(getFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((filters: any) => {
        this.filtersPayload = filters;
        this.advanceFilters = Object.assign({}, filters);
        this.dateType = LeadDateType[this.filtersPayload.DateType];
        this.filterDate = [filters?.FromDate, filters?.ToDate];
      });
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canViewLeadSource = permissionsSet.has(
          'Permissions.Leads.ViewLeadSource'
        );
      });

    this.allSubStatusList = this.masterLeadStatus?.map(
      (status: any) => status.childTypes
    );
    if (this.allSubStatusList) {
      this.allSubStatusList = Object.values(this.allSubStatusList).flat();
    }
    this.masterLeadStatus = this.masterLeadStatus
      ?.slice()
      .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));

    this.store
      .select(getReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.reportees = data;
        this.reportees = this.reportees?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.reportees = assignToSort(this.reportees, '');
      });

    this.store
      .select(getReporteesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.reporteesIsLoading = data;
      });

    this.store
      .select(getPropertyList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        this.propertyList = data
          .map((item) => String(item))
          .slice()
          .sort((a: string, b: string) => a.localeCompare(b));
      });

    this.store
      .select(getPropertyListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.propertyListIsLoading = isLoading;
      });

    this.store
      .select(getSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allSubSourceList = data;
        this.subSourceList = Object.values(data)
          .flat()
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store
      .select(getSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.subSourceListIsLoading = data;
      });

    this.store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.projectListIsLoading = data;
      });
    this.updateSubStatus();
    this.maxDate = new Date();

    // this.store
    // .select(getWhatsAppList)
    // .pipe(takeUntil(this.stopper))
    // .subscribe((data: any) => {
    //   this.whatsAppData = data;
    // });
  }

  updateSubStatus() {
    if (this.advanceFilters?.StatusesIds?.length) {
      this.subStatusList = this.allSubStatusList
        .filter((subStatus: any) =>
          this.advanceFilters.StatusesIds.includes(subStatus.baseId)
        )
        .slice()
        .sort((a: any, b: any) => a.displayName.localeCompare(b.displayName));
    } else {
      this.subStatusList = [];
      this.subStatusList = Object.values(this.allSubStatusList)
        .flat()
        .slice()
        .sort((a: any, b: any) => a.displayName.localeCompare(b.displayName));
    }
  }

  updateSubSource() {
    if (this.advanceFilters?.Sources?.length) {
      this.subSourceList = [];
      this.advanceFilters?.Sources.forEach((i: any) => {
        const source: any = LeadSource[i];
        const leadSource = IntegrationSource[source];
        this.subSourceList.push.apply(
          this.subSourceList,
          this.allSubSourceList[leadSource] || []
        );
      });
    } else {
      this.subSourceList = Object.values(this.allSubSourceList).flat();
    }
  }

  fetchCustomStatuses() {
    this.store
      .select(getCustomStatusList)
      .pipe(takeUntil(this.stopper))
      .subscribe((customStatus: any) => {
        this.customStatusList = customStatus;
      });

    this.store
      .select(getCustomStatusListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isCustomStatusListLoading = isLoading;
      });
  }

  applyAdvancedFilter() {
    this.filtersPayload = {
      ...this.filtersPayload,
      ...this.advanceFilters,
      PageNumber: 1,
    };
    this.store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this.clearDataAndFilter();
    this.modalService.hide();
  }

  onClearAllFilters() {
    this.advanceFilters = {
      ...WAInitialFilterState.filtersPayload,
    };
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}

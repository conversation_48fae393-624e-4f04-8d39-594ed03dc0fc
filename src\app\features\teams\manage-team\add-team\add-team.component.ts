import { Component, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { assignToSort, atLeastOneSelectedValidator, validateAllFormFields } from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import { AddTeams, UpdateTeams } from 'src/app/reducers/teams/teams.actions';
import { getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'add-team',
  templateUrl: './add-team.component.html'
})
export class AddTeamComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  addTeamForm: FormGroup;
  selectedTeam: any;
  usersList: any[];
  filteredMembersList: any[] = [];
  filteredLeaderList: any[] = [];
  inactiveUsers: any[] = [];
  constructor(
    private fb: FormBuilder,
    private store: Store<AppState>,
    private modalRef: BsModalRef,
    public modalService: BsModalService,
  ) { }

  ngOnInit(): void {
    this.addTeamForm = this.fb.group({
      name: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      leader: [null, Validators.required],
      members: [null, [atLeastOneSelectedValidator]],
    });

    this.addTeamForm.get('leader').valueChanges.subscribe(() => {
      this.updateFilteredLists();
    });

    this.addTeamForm.get('members').valueChanges.subscribe(() => {
      this.updateFilteredLists();
    });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.usersList = data;
        this.usersList = this.usersList?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.inactiveUsers = this.usersList?.filter((user: any) => !user.isActive);
        this.usersList = assignToSort(this.usersList, '');
        this.updateFilteredLists();
      });

    if (this.selectedTeam) {
      this.addTeamForm.patchValue({
        name: this.selectedTeam.teamName,
        leader: this.selectedTeam?.manager?.id,
        members: this.selectedTeam?.users?.map((user: any) => user?.id),
      });
    }
  }

  onUserSelect(event: any) {
    const lastUser = event[event.length - 1];
    if (lastUser && !lastUser.isActive) {
      event.pop();
    }
    const newlySelectedItems = event?.map((item: any) => item?.id);
    this.addTeamForm?.get('members')?.setValue(newlySelectedItems);
  }

  updateFilteredLists(): void {
    if (!this.usersList) return;

    const currentLeaderId = this.addTeamForm.get('leader').value;
    const currentMemberIds = this.addTeamForm.get('members').value || [];

    this.filteredMembersList = this.usersList.filter(user =>
      user.id !== currentLeaderId
    );

    this.filteredLeaderList = this.usersList.filter(user =>
      !currentMemberIds.includes(user.id)
    );
  }

  onSave() {
    if (!this.addTeamForm.valid) {
      validateAllFormFields(this.addTeamForm);
      return;
    }
    const teamData = this.addTeamForm.value;
    const payload = {
      name: teamData.name,
      userIds: teamData?.members || [this.selectedTeam?.assignedUserIds],
      ...(this.selectedTeam && {
        id: this.selectedTeam.id,
      }),
      manager: teamData?.leader
    };
    if (this.selectedTeam) {
      this.store.dispatch(new UpdateTeams(payload));
    } else {
      this.store.dispatch(new AddTeams(payload));

    }
    this.modalRef.hide();
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}

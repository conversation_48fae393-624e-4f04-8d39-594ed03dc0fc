import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AgGridModule } from 'ag-grid-angular';
import { HttpClient } from '@angular/common/http';
import {
  TranslateModule,
  TranslateLoader,
  TranslateService,
} from '@ngx-translate/core';
import { OwlDateTimeModule } from '@danielmoncada/angular-datetime-picker';
import { GoogleMapsModule } from '@angular/google-maps';

import {
  ROLES_DECLARATIONS,
  TeamsRoutingModule,
} from 'src/app/features/teams/teams-routing.module';
import { HttpLoaderFactory, playerFactory } from 'src/app/app.imports';
import { LottieModule } from 'ngx-lottie';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';
import { SharedModule } from 'src/app/shared/shared.module';

@NgModule({
  declarations: [...ROLES_DECLARATIONS],
  imports: [
    CommonModule,
    TeamsRoutingModule,
    SharedModule,
    FormsModule,
    OwlDateTimeModule,
    ReactiveFormsModule,
    AgGridModule,
    GoogleMapsModule,
    NgxMatIntlTelInputComponent,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
      // isolate: true,
    }),
    LottieModule.forRoot({ player: playerFactory })
  ],
  providers: [TranslateService],
})
export class TeamsModule { }

$default-margin-spacer: 10px;
$default-padding-spacer: 4px;

$margin-spacers: ();
$margin-spacers: map-merge(
  (
    2: 2px,
    4: (
      $default-padding-spacer * 1,
    ),
    6: 6px,
    8: (
      $default-padding-spacer * 2,
    ),
    10: (
      $default-margin-spacer * 1,
    ),
    12: (
      $default-padding-spacer * 3,
    ),
    16: (
      $default-padding-spacer * 4,
    ),
    20: (
      $default-margin-spacer * 2,
    ),
    24: (
      $default-padding-spacer * 6,
    ),
    28: (
      $default-padding-spacer * 7,
    ),
    30: (
      $default-margin-spacer * 3,
    ),
    32: (
      $default-padding-spacer * 8,
    ),
    36: (
      $default-padding-spacer * 9,
    ),
    40: (
      $default-margin-spacer * 4,
    ),
    50: (
      $default-margin-spacer * 5,
    ),
    56: (
      $default-padding-spacer * 14,
    ),
    60: (
      $default-margin-spacer * 6,
    ),
    70: (
      $default-margin-spacer * 7,
    ),
    80: (
      $default-margin-spacer * 8,
    ),
    90: (
      $default-margin-spacer * 9,
    ),
    100: (
      $default-margin-spacer * 10,
    ),
    0: 0,
  ),
  $margin-spacers
);

$padding-spacers: ();
$padding-spacers: map-merge(
  (
    2: 2px,
    4: (
      $default-padding-spacer * 1,
    ),
    5: 5px,
    6: 6px,
    8: (
      $default-padding-spacer * 2,
    ),
    9: 9px,
    10: (
      $default-margin-spacer * 1,
    ),
    12: (
      $default-padding-spacer * 3,
    ),
    16: (
      $default-padding-spacer * 4,
    ),
    20: (
      $default-margin-spacer * 2,
    ),
    24: (
      $default-padding-spacer * 6,
    ),
    28: (
      $default-padding-spacer * 7,
    ),
    30: (
      $default-margin-spacer * 3,
    ),
    32: (
      $default-padding-spacer * 8,
    ),
    36: (
      $default-padding-spacer * 9,
    ),
    40: (
      $default-margin-spacer * 4,
    ),
    50: (
      $default-margin-spacer * 5,
    ),
    53: 53px,
    54: 54px,
    60: (
      $default-margin-spacer * 6,
    ),
    68: (
      $default-padding-spacer * 17,
    ),
    70: (
      $default-margin-spacer * 7,
    ),
    80: (
      $default-margin-spacer * 8,
    ),
    90: (
      $default-margin-spacer * 9,
    ),
    100: (
      $default-margin-spacer * 10,
    ),
    0: 0,
  ),
  $padding-spacers
);

@each $prop, $abbrev in (margin: m) {
  @each $size, $length in $margin-spacers {
    .#{$abbrev}-#{$size} {
      #{$prop}: $length !important;
    }

    .#{$abbrev}t-#{$size},
    .#{$abbrev}y-#{$size} {
      #{$prop}-top: $length !important;
    }

    .#{$abbrev}r-#{$size},
    .#{$abbrev}x-#{$size} {
      #{$prop}-right: $length !important;
    }

    .#{$abbrev}b-#{$size},
    .#{$abbrev}y-#{$size} {
      #{$prop}-bottom: $length !important;
    }

    .#{$abbrev}l-#{$size},
    .#{$abbrev}x-#{$size} {
      #{$prop}-left: $length !important;
    }
  }
}

@each $prop, $abbrev in (padding: p) {
  @each $size, $length in $padding-spacers {
    .#{$abbrev}-#{$size} {
      #{$prop}: $length !important;
    }

    .#{$abbrev}t-#{$size},
    .#{$abbrev}y-#{$size} {
      #{$prop}-top: $length !important;
    }

    .#{$abbrev}r-#{$size},
    .#{$abbrev}x-#{$size} {
      #{$prop}-right: $length !important;
    }

    .#{$abbrev}b-#{$size},
    .#{$abbrev}y-#{$size} {
      #{$prop}-bottom: $length !important;
    }

    .#{$abbrev}l-#{$size},
    .#{$abbrev}x-#{$size} {
      #{$prop}-left: $length !important;
    }
  }
}

.m-auto {
  margin: auto !important;
}

.mt-auto,
.my-auto {
  margin-top: auto !important;
}

.mr-auto,
.mx-auto {
  margin-right: auto !important;
}

.mb-auto,
.my-auto {
  margin-bottom: auto !important;
}

.ml-auto,
.mx-auto {
  margin-left: auto !important;
}

.clear-margin {
  @extend .m-0;
}

.clear-margin-t,
.clear-margin-y {
  @extend .mt-0;
}

.clear-margin-r,
.clear-margin-x {
  @extend .mr-0;
}

.clear-margin-b,
.clear-margin-y {
  @extend .mb-0;
}

.clear-margin-l,
.clear-margin-x {
  @extend .ml-0;
}

.p-auto {
  padding: auto !important;
}

.pt-auto,
.py-auto {
  padding-top: auto !important;
}

.pr-auto,
.px-auto {
  padding-right: auto !important;
}

.pb-auto,
.py-auto {
  padding-bottom: auto !important;
}

.pl-auto,
.px-auto {
  padding-left: auto !important;
}

// clear padding styles
.clear-padding {
  @extend .p-0;
}

.clear-padding-t,
.clear-padding-y {
  @extend .pt-0;
}

.clear-padding-r,
.clear-padding-x {
  @extend .pr-0;
}

.clear-padding-b,
.clear-padding-y {
  @extend .pb-0;
}

.clear-padding-l,
.clear-padding-x {
  @extend .pl-0;
}

$values: () !default;
$values: map-merge(
  (
    0: 0px,
    1: 1px,
    2: 2px,
    3: 3px,
    4: 4px,
    5: 5px,
    6: 6px,
    8: 8px,
    9: 9px,
    10: 10px,
    11: 11px,
    12: 12px,
    13: 13px,
    14: 14px,
    15: 15px,
    16: 16px,
    18: 18px,
    20: 20px,
    22: 22px,
    24: 24px,
    28: 28px,
    25pr: 25%,
    25: 25px,
    30: 30px,
    33: 33px,
    40pr: 40%,
    40: 40px,
    45: 45px,
    45pr: 45%,
    48: 48px,
    50: 50%,
    50px: 50px,
    55: 55px,
    60: 60px,
    65: 65px,
    70: 70px,
    75: 75px,
    80: 80px,
    90: 90px,
    95: 95px,
    100: 100px,
    110: 110px,
    115: 115px,
    125: 125px,
    140: 140px,
    150: 150px,
    155: 155px,
    160: 160px,
    165: 165px,
    170: 170px,
    190: 190px,
    200: 200px,
    215: 215px,
    220: 220px,
    230: 230px,
    240: 240px,
    250: 250px,
    265: 265px,
    270: 270px,
    285: 285px,
    295: 295px,
    300: 300px,
    315: 315px,
    320: 320px,
    330: 330px,
    340: 340px,
  ),
  $values
);

@each $prop,
  $abbrev
    in (
      left: left,
      top: top,
      right: right,
      bottom: bottom,
      border-radius: br,
      border-top-left-radius: brtl,
      border-top-right-radius: brtr,
      border-bottom-right-radius: brbr,
      border-bottom-left-radius: brbl
    )
{
  @each $size, $length in $values {
    .#{$abbrev}-#{$size} {
      #{$prop}: $length !important;
    }
  }
}

@each $prop, $abbrev in (left: nleft, top: ntop, right: nright, bottom: nbottom)
{
  @each $size, $length in $values {
    .#{$abbrev}-#{$size} {
      #{$prop}: -$length !important;
    }
  }
}

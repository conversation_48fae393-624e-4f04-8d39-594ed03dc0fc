@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap');
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Lexend+Deca:wght@100..900&display=swap');

$base-font-size: 11;

h6,
.h6,
h5,
.h5,
h4,
.h4,
h3,
.h3,
h2,
.h2,
h1,
.h1 {
  margin: 0 !important;
}

p {
  @extend .text-normal;
  margin-bottom: 0;
}

.text-largest {
  font-size: $base-font-size + 29px;
  line-height: $base-font-size + 38px;
}

.text-xxl {
  font-size: $base-font-size + 19px;
  line-height: $base-font-size + 28px;
}

//headers & titles
h1,
.header-1 {
  font-size: $base-font-size + 13px;
  line-height: $base-font-size + 17px;
  font-weight: 400;
}

h2,
.header-2 {
  font-size: $base-font-size + 9px;
  line-height: $base-font-size + 13px;
  font-weight: 400;
}

h3,
.header-3 {
  font-size: $base-font-size + 5px !important;
  line-height: $base-font-size + 9px;
  font-weight: 400;
}

h4,
.header-4,
.text-xl {
  font-size: $base-font-size + 3px !important;
  line-height: $base-font-size + 8px;
  font-weight: 400;
}

h5,
.text-large,
.header-5 {
  font-size: $base-font-size + 1px;
  line-height: $base-font-size + 5px;
}

.text-normal,
.text-body,
h6,
.header-6 {
  font-size: $base-font-size + 0px;
  line-height: $base-font-size + 4px;
}

.text-sm {
  font-size: $base-font-size - 1px;
  line-height: $base-font-size + 2px;
}

.text-xs {
  font-size: $base-font-size - 2px;
  line-height: $base-font-size + 1px;
}

.text-xxs {
  font-size: $base-font-size - 3px;
  line-height: $base-font-size + 1px;
}

.text-xxxs {
  font-size: $base-font-size - 4px;
  line-height: $base-font-size - 2px;
}

//font weights
//fw-light
.fw-300 {
  font-weight: 300;
}

//fw-normal
.fw-400 {
  font-weight: 400 !important;
}

.fw-semi-bold {
  font-weight: 500 !important;
}

//fw-semibold
.fw-600 {
  font-weight: 600 !important;
}

//fw-bold
.fw-700 {
  font-weight: 700;
}

.fw-800 {
  font-weight: 800;
}

.fw-900 {
  font-weight: 900;
}

//text plain colors
.text-white {
  color: $white;
}

.text-light {
  color: $white;
}

.text-dark {
  color: $dark-200;
}

.text-dark-110 {
  color: $dark-110;
}

.text-dark-150 {
  color: $dark-150;
}

.text-dark-250 {
  color: $dark-250;
}

.text-dark-800 {
  color: $dark-800 !important;
}

.text-coal {
  color: $primary-black;
}

.text-black-10 {
  color: $black-10;
}

.text-black-20 {
  color: $black-20;
}

.text-black-100 {
  color: $black-100;
}

.text-black-200 {
  color: $black-200;
}

.text-black-400 {
  color: $dark-400;
}

.text-mud {
  color: $dark-700;
}

.text-gray-90 {
  color: $slate-90;
}

.text-gray-110 {
  color: $slate-110;
}

.text-slate {
  color: $slate-600;
}

.text-slate-160 {
  color: $slate-160;
}

.text-gray {
  color: $slate-600 !important;
}

.text-gray-850 {
  color: $dark-850 !important;
}

.text-dark-gray {
  color: $slate-250 !important;
}

.text-dark-gray-350 {
  color: $dark-350;
}

.text-light-gray {
  color: $slate-60;
}

.text-light-slate {
  color: $slate-40;
}

//text colors
.text-blue {
  color: var(--primary-theme-color);
}

.text-dark-blue {
  color: $blue-80;
}

.text-dark-blue-300 {
  color: $dark-blue-300;
}

.text-blue-700 {
  color: $blue-700;
}

.text-blue-1000 {
  color: $blue-1000;
}

.text-light-aqua {
  color: $accent-blue-light;
}

.text-aqua-450 {
  color: $aqua-450;
}

.text-aqua-750 {
  color: $aqua-750;
}

.text-aqua-850 {
  color: $aqua-850;
}

.text-accent-blue {
  color: $accent-blue-light !important;
}

.text-blue-light {
  color: $blue-20;
}

.text-aqua-100 {
  color: $aqua-100;
}

.text-navy-50 {
  color: $navy-50;
}

.text-navy-100 {
  color: $navy-100;
}

.text-orange {
  color: $orange-200;
}

.text-orange-800 {
  color: $orange-800;
}

.text-light-orange {
  color: $light-orange;
}

.text-dark-orange {
  color: $orange-400;
}

.text-yellow {
  color: $yellow-300;
}

.text-yellow-500 {
  color: $yellow-500;
}

.text-dark-yellow {
  color: $dark-yellow !important;
}

.text-red {
  color: $dark-red !important;
}

.text-dark-red {
  color: $red-60;
}

.text-error-red {
  color: $red-10;
}

.text-accent-red {
  color: $red-900 !important;
}

.text-red-110 {
  color: $red-110 !important;
}

.text-red-350 {
  color: $red-350 !important;
}

.text-red-450 {
  color: $red-450 !important;
}

.text-red-550 {
  color: $red-550 !important;
}

.text-red-850 {
  color: $red-850 !important;
}

.text-green {
  color: $light-green;
}

.text-light-green {
  color: $light-green-300;
}

.text-dark-green {
  color: $green-20;
}

.text-green-170 {
  color: $green-170;
}

.text-green-550 {
  color: $green-550;
}

.text-green-900 {
  color: $green-900;
}

.text-lime {
  color: $green-10;
}

.text-purple {
  color: $accent-purple;
}

.text-purple-300 {
  color: $purple-300;
}

.text-pink-600 {
  color: $pink-600;
}

.text-accent-green {
  color: $accent-green !important;
}

.text-hover {
  &:hover {
    @extend .fw-semi-bold;
  }
}

.text-dark-hover {
  &:hover {
    color: $black !important;
  }
}

.text-red-hover {
  &:hover {
    @extend .fw-semi-bold, .text-red;

    .ic-pale {
      color: $dark-red;
    }
  }
}

.text-green-250 {
  color: $accent-green;
}

.text-violet-500 {
  color: $violet-500
}
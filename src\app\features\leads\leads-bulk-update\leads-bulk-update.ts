import {
  Component,
  EventEmitter,
  Input,
  On<PERSON><PERSON><PERSON>,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { GridApi } from 'ag-grid-community';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { combineLatest, filter, skipWhile, take, takeUntil } from 'rxjs';

import { BsDropdownDirective } from 'ngx-bootstrap/dropdown';
import {
  EMPTY_GUID,
  PAGE_SIZE,
  VALIDATION_CLEAR,
  VALIDATION_SET,
} from 'src/app/app.constants';
import {
  BulkType,
  LeadAssignmentType,
  LeadSource,
  LeadVisibility
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  BulkAssignLead,
  Lead,
  LeadsFilter,
} from 'src/app/core/interfaces/leads.interface';
import {
  assignToSort,
  getAssignedToDetails,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { BulkLeadsEmailShareComponent } from 'src/app/features/leads/bulk-leads-email-share/bulk-leads-email-share.component';
import { DuplicateAssignInfoComponent } from 'src/app/features/leads/duplicate-assign-info/duplicate-assign-info.component';
import { LeadBulkShareComponent } from 'src/app/features/leads/lead-bulk-share/lead-bulk-share.component';
import { FetchAllSources, FetchDuplicateSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.actions';
import {
  getAllSources,
  getAllSourcesLoading,
  getDuplicateSettingsAnonymous,
  getGlobalSettingsAnonymous,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  BulkAgency,
  BulkCampaign,
  BulkChannelPartner,
  BulkProjects,
  BulkReassignLead,
  BulkSource,
  DeleteLeads,
  FetchAgencyNameList,
  FetchBulkOperation,
  FetchCampaignList,
  FetchChannelPartnerList,
  FetchProjectList,
  FetchSubSourceList,
  PermanentDeleteLeads,
  RestoreLeads,
  SecondaryAssignLead,
  UpdateFilterPayload,
} from 'src/app/reducers/lead/lead.actions';
import {
  getAgencyNameList,
  getAgencyNameListIsLoading,
  getBulk2ndReassignDetails,
  getBulk2ndReassignIsLoading,
  getBulkAgencyIsLoading,
  getBulkCampaignIsLoading,
  getBulkDeleteLeadsIsLoading,
  getBulkProjectIsLoading,
  getBulkReassignDetails,
  getBulkReassignLeadIsLoading,
  getBulkRestoreLeadsIsLoading,
  getBulkSourceIsLoading,
  getCampaignList,
  getCampaignListIsLoading,
  getChannelPartnerList,
  getChannelPartnerListIsLoading,
  getDuplicateFeature,
  getFiltersPayload,
  getLeads,
  getProjectList,
  getProjectListIsLoading,
  getSubSourceList,
  getSubSourceListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchAdminsAndReportees } from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getAdminsAndReporteesIsLoading,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { BulkOperationTrackerComponent } from 'src/app/shared/components/bulk-operation-tracker/bulk-operation-tracker.component';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'leads-bulk-update',
  templateUrl: './leads-bulk-update.html',
})
export class LeadsBulkUpdateComponent implements OnInit, OnDestroy, OnChanges {
  stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('trackerInfoModal') trackerInfoModal: any;
  @ViewChild('moreActionsDropdownRef', { static: false }) moreActionsDropdown!: BsDropdownDirective;
  isDropdownOpen: boolean = false;
  visibleButtons: any[] = []
  sameStatusRows: any[];
  bulkReassignForm: FormGroup;
  bulkSourceForm: FormGroup;
  bulkSecondaryReassignForm: FormGroup;
  bulkProjectFrom: FormGroup;
  bulkAgencyFrom: FormGroup;
  bulkChannelPartnerForm: FormGroup;
  bulkCampaignForm: FormGroup;
  selectedChannelPartners: any;
  selectedCampaigns: any;
  campaignList: any;
  campaignListIsLoading: boolean = false;
  channelPartnerList: any;
  channelPartnerListIsLoading: boolean = false;
  selectedBulkReassign: any;
  selectedSecondaryBulkReassign: any;
  EMPTY_GUID = EMPTY_GUID;
  isDuplicateFeature: boolean;
  isUnassignLeadSelected: boolean;
  isLeadNameColInAscOrder: boolean = false;
  isAssignToColInAscOrder: boolean = false;
  selectSecondaryUser: any;

  canEditLead: boolean = false;
  canAssignLead: boolean = false;
  canDeleteLead: boolean = false;
  canAssignToAny: boolean = false;
  canUpdateSource: boolean = false;
  canViewLeadSource: boolean = false;
  canCommunicate: boolean = false;
  canUpdateInvoice: boolean = false;
  canAssignInvoice: boolean = false;
  canDeleteInvoice: boolean = false;
  isDualOwnershipEnabled: boolean = false;
  leadSources: Array<any> = []
  LeadSource = LeadSource;
  selectedLeads: any;
  assignToUsersList: { label: string; value: string }[];
  sortedActiveUsers: Array<Object> = [];
  activeUsers: Array<Object> = [];
  assignedToUserId: any;
  allUsers: any[] = [];
  allSubSourceList: any;
  subSourceList: any;
  selectedSources: any;
  projectList: Array<string> = [];
  selectedProjects: any;
  agencyList: Array<string> = [];
  selectedAgencies: any;
  filtersPayload: LeadsFilter;
  pageSize: number = PAGE_SIZE;
  currOffset: number = 0;
  leadAssignmentType = LeadAssignmentType;
  assignmentType: LeadAssignmentType = LeadAssignmentType.WithHistory;
  assignmentTypeOptions = [
    { value: this.leadAssignmentType.WithHistory, label: 'With history' },
    { value: this.leadAssignmentType.WithoutHistory, label: 'Without history' },
    {
      value: this.leadAssignmentType.WithoutHistoryWithNewStatus,
      label: 'Without history and new status',
    },
  ];
  @Input() appliedFilter: any;
  @Input() selectedNodes: any;
  @Input() gridApi: GridApi;

  usersListForReassignmentIsLoading: boolean = true;
  adminsAndReporteesIsLoading: boolean = true;
  subSourceListIsLoading: boolean = true;
  bulkProjectsIsLoading: boolean = false;
  bulkAgencyIsLoading: boolean = false;
  bulkCampaignIsLoading: boolean = false;
  bulkCPIsLoading: boolean = false;
  projectListIsLoading: boolean = true;
  agencyListIsLoading: boolean = true;
  bulkReassignLeadsIsLoading: boolean = false;
  bulk2ndReassignIsLoading: boolean = false;
  bulkSourceIsLoading: boolean = false;
  bulkDeleteIsLoading: boolean = false;
  bulkRestoreIsLoading: boolean = false;
  isGlobalSettingsFetched: boolean = false;
  isDuplicateFeatureAdded: boolean = false;
  canUpdateStatus: boolean = false;
  isDuplicateFeatureSettingsFetched: boolean = false;
  globalSettingsData: any;
  message: string = '';
  currentPath: string;
  bulkPermanentDelete: boolean = false;
  type: string;
  canBulkUpdateStatus: boolean = false;
  canBulkReassign: boolean = false;
  canBulkSecondaryReassign: boolean = false;
  canBulkProjectAssignment: boolean = false;
  canBulkUpdateSource: boolean = false;
  canBulkRestore: boolean = false;
  canBulkDelete: boolean = false;
  canBulkWhatsapp: boolean = false;
  canBulkEmail: boolean = false;

  canBulkUpdateStatusInvoice: boolean = false;
  canBulkReassignInvoice: boolean = false;
  canBulkSecondaryReassignInvoice: boolean = false;
  canBulkUpdateSourceInvoice: boolean = false;
  canBulkDeleteInvoice: boolean = false;
  canBulkRestoreInvoice: boolean = false;
  canBulkProjectAssignmentInvoice: boolean = false;
  canBulkWhatsAppInvoice: boolean = false;
  canBulkEmailInvoice: boolean = false;
  leadVisibilityEnum: any = LeadVisibility;
  isSourcesLoading: boolean;
  canBulkAgency: boolean = false;
  canBulkCampaign: boolean = false;
  canBulkChannelPartner: boolean = false;
  canBulkAgencyInvoice: boolean = false;
  canBulkCampaignInvoice: boolean = false;
  canBulkChannelPartnerInvoice: boolean = false;

  constructor(
    public modalService: BsModalService,
    private formBuilder: FormBuilder,
    private _store: Store<AppState>,
    private translateService: TranslateService,
    private modalRef: BsModalRef,
    private bulkUpdateModalRef: BsModalRef,
    private bulkReassignModalRef: BsModalRef,
    private bulkSourceModalRef: BsModalRef,
    private bulkProjectModalRef: BsModalRef,
    private bulkAgencyModalRef: BsModalRef,
    private bulkDeleteModalRef: BsModalRef,
    private bulkSecondaryReassignModalRef: BsModalRef,
    private _notificationsService: NotificationsService,
    public router: Router,
    public trackingService: TrackingService,
    private bulkChannelPartnerModalRef: BsModalRef,
    private bulkCampaignModalRef: BsModalRef,
  ) {
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canEditLead = permissionsSet.has('Permissions.Leads.Update');
        this.canAssignLead = permissionsSet.has('Permissions.Leads.Assign');
        this.canDeleteLead = permissionsSet.has('Permissions.Leads.Delete');
        this.canViewLeadSource = permissionsSet.has(
          'Permissions.Leads.ViewLeadSource'
        );
        this.canUpdateStatus = permissionsSet.has(
          'Permissions.Leads.UpdateLeadStatus'
        );
        this.canUpdateSource = permissionsSet.has(
          'Permissions.Leads.UpdateSource'
        );
        this.canCommunicate = permissionsSet.has(
          'Permissions.Leads.Communications'
        );

        this.canBulkUpdateStatus = permissionsSet.has(
          'Permissions.Leads.BulkUpdateStatus'
        );
        this.canBulkReassign = permissionsSet.has(
          'Permissions.Leads.BulkReassign'
        );
        this.canBulkSecondaryReassign = permissionsSet.has(
          'Permissions.Leads.BulkSecondaryReassign'
        );
        this.canBulkProjectAssignment = permissionsSet.has(
          'Permissions.Leads.BulkProjectAssignment'
        );
        this.canBulkUpdateSource = permissionsSet.has(
          'Permissions.Leads.BulkUpdateSource'
        );
        this.canBulkDelete = permissionsSet.has('Permissions.Leads.BulkDelete');
        this.canBulkRestore = permissionsSet.has(
          'Permissions.Leads.BulkRestore'
        );
        this.canBulkWhatsapp = permissionsSet.has(
          'Permissions.Leads.BulkWhatsApp'
        );
        this.canBulkEmail = permissionsSet.has('Permissions.Leads.BulkEmail');
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.canAssignToAny = true;
        } else {
          this._store.dispatch(new FetchAdminsAndReportees());
        }
        this.canUpdateInvoice = permissionsSet.has(
          'Permissions.Invoice.Update'
        );
        this.canDeleteInvoice = permissionsSet.has(
          'Permissions.Invoice.Delete'
        );
        this.canAssignInvoice = permissionsSet.has(
          'Permissions.Invoice.Assign'
        );

        this.canBulkUpdateStatusInvoice = permissionsSet.has(
          'Permissions.Invoice.BulkUpdateStatus'
        );
        this.canBulkReassignInvoice = permissionsSet.has(
          'Permissions.Invoice.BulkReassign'
        );
        this.canBulkSecondaryReassignInvoice = permissionsSet.has(
          'Permissions.Invoice.BulkSecondaryReassign'
        );
        this.canBulkUpdateSourceInvoice = permissionsSet.has(
          'Permissions.Invoice.BulkUpdateSource'
        );
        this.canBulkProjectAssignmentInvoice = permissionsSet.has(
          'Permissions.Invoice.BulkProjectAssignment'
        );
        this.canBulkWhatsAppInvoice = permissionsSet.has(
          'Permissions.Invoice.BulkWhatsApp'
        );
        this.canBulkEmailInvoice = permissionsSet.has(
          'Permissions.Invoice.BulkEmail'
        );
        this.canBulkDeleteInvoice = permissionsSet.has(
          'Permissions.Invoice.BulkDelete'
        );
        this.canBulkRestoreInvoice = permissionsSet.has(
          'Permissions.Invoice.BulkRestore'
        );
        this.canBulkAgencyInvoice = permissionsSet.has(
          'Permissions.Invoice.BulkUpdateAgency'
        );
        this.canBulkCampaignInvoice = permissionsSet.has(
          'Permissions.Invoice.BulkUpdateCampaign'
        );
        this.canBulkChannelPartnerInvoice = permissionsSet.has(
          'Permissions.Invoice.BulkUpdateChannelPartner'
        );
        this.canBulkAgency = permissionsSet.has(
          'Permissions.Leads.BulkUpdateAgency'
        );
        this.canBulkCampaign = permissionsSet.has(
          'Permissions.Leads.BulkUpdateCampaign'
        );
        this.canBulkChannelPartner = permissionsSet.has(
          'Permissions.Leads.BulkUpdateChannelPartner'
        );
        this.updateVisibleButtons();
      });
    this._store
      .select(getSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allSubSourceList = data;
        this.subSourceList = Object.values(data)
          .flat()
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
        this.onSourceChange();
        this.updateSubSourceForBulkSource();
      });
    this._store
      .select(getSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.subSourceListIsLoading = data;
      });
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (Object.keys(data || {}).length) {
          this.isGlobalSettingsFetched = true;
          this.globalSettingsData = data;
          this.isDualOwnershipEnabled = data?.isDualOwnershipEnabled;
          this.updateVisibleButtons();
        }
      });
    this._store
      .select(getDuplicateSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (Object.keys(data || {}).length) {
          this.isDuplicateFeatureSettingsFetched = true;
          this.isDuplicateFeatureAdded = data?.isFeatureAdded;
        }
      });
  }

  ngOnChanges(changes: any): void {
    if (changes.appliedFilter && !changes.appliedFilter.firstChange) {
      this.updateVisibleButtons();
    }
  }

  ngOnInit(): void {
    this._store.dispatch(new FetchDuplicateSettingsAnonymous());
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;
    const leads$ = this._store.select(getLeads).pipe(takeUntil(this.stopper));

    const filtersPayload$ = this._store
      .select(getFiltersPayload)
      .pipe(takeUntil(this.stopper));

    const adminsAndReportees$ = this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper));

    const usersListForReassignment$ = this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper));

    const duplicateFeature$ = this._store
      .select(getDuplicateFeature)
      .pipe(takeUntil(this.stopper));

    const projectList$ = this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper));
    const agencyList$ = this._store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper));
    const campaignList$ = this._store
      .select(getCampaignList)
      .pipe(takeUntil(this.stopper));
    const campaignListIsLoading$ = this._store
      .select(getCampaignListIsLoading)
      .pipe(takeUntil(this.stopper));
    const channelPartnerList$ = this._store
      .select(getChannelPartnerList)
      .pipe(takeUntil(this.stopper));
    const channelPartnerListIsLoading$ = this._store
      .select(getChannelPartnerListIsLoading)
      .pipe(takeUntil(this.stopper));

    const projectListIsLoading$ = this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper));

    const agencyListIsLoading$ = this._store
      .select(getAgencyNameListIsLoading)
      .pipe(takeUntil(this.stopper));
    combineLatest({
      adminsAndReportees: adminsAndReportees$,
      usersListForReassignment: usersListForReassignment$,
      projectList: projectList$,
      agencyList: agencyList$,
      duplicateFeature: duplicateFeature$,
      filtersPayload: filtersPayload$,
      leads: leads$,
      projectListIsLoading: projectListIsLoading$,
      agencyListIsLoading: agencyListIsLoading$,
      campaignList: campaignList$,
      campaignListIsLoading: campaignListIsLoading$,
      channelPartnerList: channelPartnerList$,
      channelPartnerListIsLoading: channelPartnerListIsLoading$,
    }).subscribe(
      ({
        adminsAndReportees,
        usersListForReassignment,
        duplicateFeature,
        projectList,
        agencyList,
        filtersPayload,
        leads,
        projectListIsLoading,
        agencyListIsLoading,
        campaignList,
        campaignListIsLoading,
        channelPartnerList,
        channelPartnerListIsLoading,
      }) => {
        this.activeUsers = this.allUsers?.filter((user: any) => user.isActive);
        this.sortedActiveUsers = assignToSort(
          this.activeUsers,
          this.assignedToUserId
        );
        this.assignToUsersList = this.sortedActiveUsers.map((user: any) => {
          return {
            label: `${user.firstName} ${user.lastName}`,
            value: user.id,
          };
        });
        if (
          this.leadVisibilityEnum[this.appliedFilter.leadVisibility] !==
          'Unassigned'
        ) {
          this.assignToUsersList.unshift({
            label: this.translateService.instant('LEADS.unassign-lead'),
            value: '00000000-0000-0000-0000-000000000000',
          });
        }
        this.isDuplicateFeature = duplicateFeature;
        if (this.canAssignToAny) {
          this.allUsers = usersListForReassignment;
        } else {
          this.allUsers = adminsAndReportees;
        }

        this.filtersPayload = filtersPayload;
        this.pageSize = this.filtersPayload?.pageSize;
        this.currOffset = this.filtersPayload?.pageNumber - 1;
        this.projectList = projectList
          .slice()
          .sort((a: any, b: any) => a?.name?.localeCompare(b?.name));
        this.agencyList = agencyList
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
        this.agencyListIsLoading = agencyListIsLoading;
        this.projectListIsLoading = projectListIsLoading;
        this.campaignList = campaignList
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
        this.campaignListIsLoading = campaignListIsLoading;
        this.channelPartnerList = channelPartnerList
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
        this.channelPartnerListIsLoading = channelPartnerListIsLoading;
      }
    );

    this._store.dispatch(new FetchAllSources());
    this._store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((leadSource: any) => {
        if (leadSource) {
          const enabledSources = leadSource
            .filter((source: any) => source.isEnabled)
            .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));
          this.leadSources = [...enabledSources];
        } else {
          this.leadSources = [];
        }
      });
    this._store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });
    this.updateVisibleButtons();
  }

  openBulkUpdateStatusModal(BulkUpdateStatusModal: TemplateRef<any>) {
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.sameStatusRows = this.selectedNodes?.map((node: any) => node.data);
    let initialState: any = {
      data: Object.values(this.selectedNodes),
      class: 'right-modal modal-500 ip-modal-unset',
    };
    this.bulkUpdateModalRef = this.modalService.show(
      BulkUpdateStatusModal,
      initialState
    );
    this.trackingService.trackFeature(
      'Web.Leads.Button.BulkUpdateStatus.Click'
    );
  }

  openBulkReassignModal(BulkReassignModal: TemplateRef<any>) {
    this.trackingService.trackFeature(
      'Web.Leads.Button.BulkReassignLeads.Click'
    );
    this.bulkReassignForm = this.formBuilder.group({
      assignmentType: 0,
      assignedToUsers: ['', Validators.required],
      isChangeSourceSelected: false,
      selectedSource: null,
      selectedSubSource: null,
      isChangeProjectSelected: false,
      selectedProject: null,
      isCreateDuplicateSelected: false,
    });

    this._store.dispatch(new FetchSubSourceList());
    this._store.dispatch(new FetchProjectList());

    this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.usersListForReassignmentIsLoading = data;
      });

    this._store
      .select(getAdminsAndReporteesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.adminsAndReporteesIsLoading = data;
      });

    this.bulkReassignForm
      .get('isChangeSourceSelected')
      .valueChanges.subscribe((val: any) => {
        if (val) {
          toggleValidation(
            VALIDATION_SET,
            this.bulkReassignForm,
            'selectedSource',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.bulkReassignForm,
            'selectedSource'
          );
        }
      });

    this.bulkReassignForm
      .get('isChangeProjectSelected')
      .valueChanges.subscribe((val: any) => {
        if (val) {
          toggleValidation(
            VALIDATION_SET,
            this.bulkReassignForm,
            'selectedProject',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.bulkReassignForm,
            'selectedProject'
          );
        }
      });
    this.selectedBulkReassign = this.gridApi
      .getSelectedNodes()
      .map((lead: any) => lead.data);
    let initialState: any = {
      data: this.selectedBulkReassign,
      class: 'modal-500 right-modal ip-modal-unset',
    };
    this.bulkReassignModalRef = this.modalService.show(
      BulkReassignModal,
      initialState
    );
    // need to check with pooran sir
    // setTimeout(() => {
    //   if (this.bulkReassignModalRef) {
    //     this.trackingService.trackFeature('Web.Leads.Page.BulkReassignLeads.Visit');
    //   }
    // }, 1000);
  }

  updateBulkAssign() {
    if (!this.bulkReassignForm.valid) {
      validateAllFormFields(this.bulkReassignForm);
      return;
    }
    this.bulkReassignLeadsIsLoading = true;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this._store.dispatch(
      new BulkReassignLead(this.prepareBulkReassignRequestBody())
    );

    this._store
      .select(getBulkReassignLeadIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => isLoading),
        take(1)
      )
      .subscribe((isLoading: boolean) => {
        this.bulkReassignLeadsIsLoading = isLoading;
        this.bulkReassignModalRef.hide();
      });
    this.selectedNodes = {};
    this.sameStatusRows = [];
    this.assignedToUserId = null;
    const numberOfLeads = this.selectedBulkReassign?.length;

    const intervalId = setInterval(() => {
      if (this.modalService.getModalsCount() === 0) {
        if (numberOfLeads >= 50) {
          this.type = 'Bulk Reassign Leads';
          this.modalRef = this.modalService.show(
            this.trackerInfoModal,
            Object.assign(
              {},
              {
                class: 'modal-400 top-modal ph-modal-unset',
                ignoreBackdropClick: true,
                keyboard: false,
              }
            )
          );
        } else {
          this._store
            .select(getBulkReassignDetails)
            .pipe(takeUntil(this.stopper))
            .subscribe((duplicateData: any) => {
              if (
                duplicateData?.items?.length &&
                this.modalService.getModalsCount() === 0
              ) {
                let initialState: any = {
                  response: duplicateData,
                  responseData: duplicateData?.items,
                };
                this.modalService.show(
                  DuplicateAssignInfoComponent,
                  Object.assign(
                    {},
                    {
                      class: 'modal-550 modal-dialog-centered ip-modal-unset',
                      initialState,
                    }
                  )
                );
              }
            });
        }
      }
      clearInterval(intervalId);
    }, 2000);

    if (this.bulkReassignModalRef) {
      this.bulkReassignModalRef.hide();
    }
  }

  onScrollableContainerScroll(): void {
    if (this.moreActionsDropdown?.isOpen) {
      this.moreActionsDropdown.hide();
    }
  }

  updateVisibleButtons() {
    const buttons = [];
    // Bulk Update Status
    if ((this.currentPath === '/invoice' ? this.canBulkUpdateStatusInvoice : this.canBulkUpdateStatus) &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Unassigned' &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Deleted') {
      buttons.push({ id: 'bulkUpdateStatus' });
    }
    // Bulk Reassign
    if ((this.currentPath === '/invoice' ? this.canBulkReassignInvoice : this.canBulkReassign) &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Deleted') {
      buttons.push({ id: 'bulkReassign' });
    }
    // Bulk Secondary Reassign
    if (this.globalSettingsData?.isDualOwnershipEnabled &&
      (this.currentPath === '/invoice' ? this.canBulkSecondaryReassignInvoice : this.canBulkSecondaryReassign) &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Deleted' &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Unassigned') {
      buttons.push({ id: 'bulkSecondaryReassign' });
    }
    // Bulk Source
    if ((this.currentPath === '/invoice' ? this.canBulkUpdateSourceInvoice : this.canBulkUpdateSource) &&
      this.canViewLeadSource &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Deleted' &&
      this.globalSettingsData?.isLeadSourceEditable) {
      buttons.push({ id: 'bulkSource' });
    }
    // Bulk Agency
    if ((this.currentPath === '/invoice' ? this.canBulkAgencyInvoice : this.canBulkAgency) &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Deleted') {
      buttons.push({ id: 'bulkAgency' });
    }
    // Bulk Channel Partner
    if ((this.currentPath === '/invoice' ? this.canBulkChannelPartnerInvoice : this.canBulkChannelPartner) &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Deleted') {
      buttons.push({ id: 'bulkChannelPartner' });
    }
    // Bulk Campaign
    if ((this.currentPath === '/invoice' ? this.canBulkCampaignInvoice : this.canBulkCampaign) &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Deleted') {
      buttons.push({ id: 'bulkCampaign' });
    }
    // Bulk Project
    if ((this.currentPath === '/invoice' ? this.canBulkProjectAssignmentInvoice : this.canBulkProjectAssignment) &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Deleted') {
      buttons.push({ id: 'bulkProject' });
    }
    // Bulk WhatsApp
    if ((this.currentPath === '/invoice' ? this.canBulkWhatsAppInvoice : this.canBulkWhatsapp) &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Unassigned' &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Deleted' &&
      this.globalSettingsData?.isWhatsAppEnabled) {
      buttons.push({ id: 'bulkWhatsapp' });
    }
    // Bulk Email
    if ((this.currentPath === '/invoice' ? this.canBulkEmailInvoice : this.canBulkEmail) &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Unassigned' &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] !== 'Deleted') {
      buttons.push({ id: 'bulkEmail' });
    }
    // Bulk Restore
    if ((this.currentPath === '/invoice' ? this.canBulkRestoreInvoice : this.canBulkRestore) &&
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] === 'Deleted') {
      buttons.push({ id: 'bulkRestore' });
    }
    // Bulk Delete
    if (this.currentPath === '/invoice' ? this.canBulkDeleteInvoice : this.canBulkDelete) {
      buttons.push({ id: 'bulkDelete' });
    }
    this.visibleButtons = buttons;
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown(): void {
    this.isDropdownOpen = false;
  }

  openBulkStatus() {
    this._store.dispatch(new FetchBulkOperation(1, 10, 'lead'));

    this.modalRef = this.modalService.show(BulkOperationTrackerComponent, {
      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState: {
        moduleType: 'lead',
      },
    });
  }

  prepareBulkReassignRequestBody(): BulkAssignLead {
    const formData = this.bulkReassignForm.value;
    const leadIds: string[] = this.selectedBulkReassign.map(
      (lead: Lead) => lead?.id
    );

    const userIds: string[] = formData.assignedToUsers?.map((user: any) =>
      !user?.value ? EMPTY_GUID : user?.value
    );

    return {
      leadIds,
      assignmentType: formData.assignmentType,
      updateSource: formData.isChangeSourceSelected,
      leadSource:
        formData.selectedSource && formData.isChangeSourceSelected
          ? formData.selectedSource.value
          : LeadSource.Direct,
      updateSubSource: !!formData.selectedSubSource,
      subSource: formData.isChangeSourceSelected
        ? formData.selectedSubSource?.label
          ? formData.selectedSubSource.label
          : formData.selectedSubSource
        : null,
      createDuplicate: formData.isCreateDuplicateSelected,
      updateProject: formData.isChangeProjectSelected,
      projects:
        formData.isChangeProjectSelected && formData?.selectedProject?.length
          ? [...formData.selectedProject]
          : null,
      userIds,
      bulkCategory: BulkType.BulkAssignment,
    };
  }

  openBulkSecondaryReassignModal(BulkSecondaryReassignModal: TemplateRef<any>) {
    this.bulkSecondaryReassignForm = this.formBuilder.group({
      secondary: ['', Validators.required],
    });
    this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.usersListForReassignmentIsLoading = data;
      });

    this._store
      .select(getAdminsAndReporteesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.adminsAndReporteesIsLoading = data;
      });
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.selectedBulkReassign = this.selectedNodes?.map(
      (node: any) => node.data
    );
    let initialState: any = {
      data: Object.values(this.selectedBulkReassign),
      class: 'right-modal modal-500 ip-modal-unset',
    };
    this.bulkSecondaryReassignModalRef = this.modalService.show(
      BulkSecondaryReassignModal,
      initialState
    );
    this.trackingService.trackFeature(
      'Web.Leads.Options.BulkSecondaryAssignLeads.Click'
    );
  }

  updateSecondaryBulkAssign() {
    this.trackingService.trackFeature('Web.Leads.Button.Save.Click');
    if (!this.bulkSecondaryReassignForm.valid) {
      validateAllFormFields(this.bulkSecondaryReassignForm);
      return;
    }
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.sameStatusRows = this.selectedNodes?.map((node: any) => node.data);
    let resource: any = {
      leadIds: this.sameStatusRows.map((item: Lead) => item.id),
    };
    const secondaryUserIds = this.bulkSecondaryReassignForm
      .get('secondary')
      .value.map((user: any) => user.value);
    if (secondaryUserIds) {
      resource = {
        ...resource,
        userIds: secondaryUserIds,
      };
      this._store.dispatch(new SecondaryAssignLead(resource));
    }
    this.bulk2ndReassignIsLoading = true;
    if (this.bulkSecondaryReassignModalRef) {
      this.bulkSecondaryReassignModalRef.hide();
    }
    this._store
      .select(getBulk2ndReassignIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.bulk2ndReassignIsLoading = isLoading;
        if (!isLoading) {
          this.bulkSecondaryReassignModalRef.hide();
        }
      });
    this.selectedNodes = {};
    this.sameStatusRows = [];
    this.bulkSecondaryReassignForm.reset();

    const intervalId = setInterval(() => {
      if (this.modalService.getModalsCount() === 0) {
        const numberOfLeads = resource?.leadIds.length;
        if (numberOfLeads >= 50) {
          this.type = 'Bulk Secondary Reassign';
          this.modalRef = this.modalService.show(
            this.trackerInfoModal,
            Object.assign(
              {},
              {
                class: 'modal-400 top-modal ph-modal-unset',
                ignoreBackdropClick: true,
                keyboard: false,
              }
            )
          );
        } else {
          this._store
            .select(getBulk2ndReassignDetails)
            .pipe(takeUntil(this.stopper))
            .subscribe((PrimaryUserDetails: any) => {
              if (
                PrimaryUserDetails?.data?.length &&
                this.modalService.getModalsCount() === 0
              ) {
                let initialState: any = {
                  response: PrimaryUserDetails,
                  responseData: PrimaryUserDetails?.data,
                  assignType: '2ndReassign',
                };
                this.modalService.show(
                  DuplicateAssignInfoComponent,
                  Object.assign(
                    {},
                    {
                      class: 'modal-550 modal-dialog-centered ip-modal-unset',
                      initialState,
                    }
                  )
                );
              }
            });
        }
      }
      clearInterval(intervalId);
    }, 2000);
    if (this.bulkSecondaryReassignModalRef) {
      this.bulkSecondaryReassignModalRef.hide();
      this.gridApi.deselectAll();
    }
  }

  openBulkSourceModal(BulkSourceModal: TemplateRef<any>) {
    this.bulkSourceForm = this.formBuilder.group({
      source: ['', Validators.required],
      subsource: [null],
    });
    this._store.dispatch(new FetchSubSourceList());
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.selectedSources = this.selectedNodes?.map((node: any) => node.data);
    let initialState: any = {
      data: Object.values(this.selectedSources),
      class: 'right-modal modal-500 ip-modal-unset',
    };
    this.bulkSourceModalRef = this.modalService.show(
      BulkSourceModal,
      initialState
    );
    this.trackingService.trackFeature('Web.Leads.Button.BulkSource.Click');
  }

  updateBulkSource() {
    if (this.bulkSourceForm.invalid) {
      validateAllFormFields(this.bulkSourceForm);
      return;
    }
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.bulkSourceIsLoading = true;
    this.sameStatusRows = this.selectedNodes?.map((node: any) => node.data);
    const selectedSource = this.bulkSourceForm.get('source').value;
    let resource: any = {
      leadIds: this.sameStatusRows.map((item: Lead) => item.id),
      leadSource: selectedSource.value,
      subSource: this.bulkSourceForm.get('subsource').value ?? null,
    };

    if (selectedSource) {
      this._store.dispatch(new BulkSource(resource));

      const intervalId = setInterval(() => {
        if (this.modalService.getModalsCount() === 0) {
          const numberOfLeads = resource?.leadIds?.length;
          if (numberOfLeads >= 50) {
            this.type = 'Bulk Source';
            this.modalRef = this.modalService.show(
              this.trackerInfoModal,
              Object.assign(
                {},
                {
                  class: 'modal-400 top-modal ph-modal-unset',
                  ignoreBackdropClick: true,
                  keyboard: false,
                }
              )
            );
          }
        }
        clearInterval(intervalId);
      }, 2000);

      this._store
        .select(getBulkSourceIsLoading)
        .pipe(
          skipWhile((isLoading: boolean) => isLoading),
          take(1)
        )
        .subscribe((isLoading: boolean) => {
          this.bulkSourceIsLoading = isLoading;
          this.bulkSourceModalRef.hide();
          this.selectedNodes = {};
          this.sameStatusRows = [];
          this.bulkSourceForm.reset();
        });

      if (this.bulkSourceModalRef) {
        this.bulkSourceModalRef.hide();
        this.gridApi.deselectAll();
      }
    }
  }

  openBulkAgencyModal(BulkAgencyModal: TemplateRef<any>) {
    this.bulkAgencyFrom = this.formBuilder.group({
      agency: [null],
      shouldRemoveExistingAgency: [false],
    });
    this._store.dispatch(new FetchAgencyNameList());
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.selectedAgencies = this.selectedNodes?.map((node: any) => node.data);
    let initialState: any = {
      data: Object.values(this.selectedAgencies),
      class: 'right-modal modal-500 ip-modal-unset',
    };
    this.bulkAgencyModalRef = this.modalService.show(
      BulkAgencyModal,
      initialState
    );
    this.trackingService.trackFeature('Web.Leads.Button.BulkProject.Click');
  }

  openBulkChannelPartnerModal(BulkChannelPartnerModal: TemplateRef<any>) {
    this.bulkChannelPartnerForm = this.formBuilder.group({
      channelPartner: [null],
      shouldRemoveExistingChannelPartener: [false],
    });
    this._store.dispatch(new FetchChannelPartnerList());
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.selectedChannelPartners = this.selectedNodes?.map((node: any) => node.data);
    let initialState: any = {
      data: Object.values(this.selectedChannelPartners),
      class: 'right-modal modal-500 ip-modal-unset',
    };
    this.bulkChannelPartnerModalRef = this.modalService.show(
      BulkChannelPartnerModal,
      initialState
    );
    this.trackingService.trackFeature('Web.Leads.Button.BulkProject.Click');
  }

  updateBulkChannelPartner() {
    if (!this.bulkChannelPartnerForm.valid) {
      validateAllFormFields(this.bulkChannelPartnerForm);
      return;
    }
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.sameStatusRows = this.selectedNodes?.map((node: any) => node.data);
    let resource: any = {
      ids: this.sameStatusRows.map((item: Lead) => item.id),
    };
    let selectedChannelPartner = this.bulkChannelPartnerForm.get('channelPartner').value;

    resource = {
      ...resource,
      channelPartenerNames: selectedChannelPartner,
      shouldRemoveExistingChannelPartener: this.bulkChannelPartnerForm?.get(
        'shouldRemoveExistingChannelPartener'
      )?.value,
    };
    this._store.dispatch(new BulkChannelPartner(resource));
    const intervalId = setInterval(() => {
      if (this.modalService.getModalsCount() === 0) {
        const numberOfLeads = resource?.ids?.length;
        if (numberOfLeads >= 50) {
          this.type = 'Bulk Campaign';
          this.modalRef = this.modalService.show(
            this.trackerInfoModal,
            Object.assign(
              {},
              {
                class: 'modal-400 top-modal ph-modal-unset',
                ignoreBackdropClick: true,
                keyboard: false,
              }
            )
          );
        }
      }
      clearInterval(intervalId);
    }, 2000);
    this.bulkCPIsLoading = true;
    this._store
      .select(getBulkCampaignIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.bulkCPIsLoading = isLoading;
        if (!isLoading) {
          this.bulkChannelPartnerModalRef.hide();
        }
      });
    this.selectedNodes = {};
    this.sameStatusRows = [];
    this.bulkChannelPartnerForm.reset();
    if (this.bulkChannelPartnerModalRef) {
      this.bulkChannelPartnerModalRef.hide();
      this.gridApi.deselectAll();
    }
  }

  openBulkCampaignModal(BulkCampaignModal: TemplateRef<any>) {
    this.bulkCampaignForm = this.formBuilder.group({
      campaign: [null],
      shouldRemoveExistingCampaign: [false],
    });
    this._store.dispatch(new FetchCampaignList());
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.selectedCampaigns = this.selectedNodes?.map((node: any) => node.data);
    let initialState: any = {
      data: Object.values(this.selectedCampaigns),
      class: 'right-modal modal-500 ip-modal-unset',
    };
    this.bulkCampaignModalRef = this.modalService.show(
      BulkCampaignModal,
      initialState
    );
    this.trackingService.trackFeature('Web.Leads.Button.BulkProject.Click');
  }

  updateBulkCampaign() {
    if (!this.bulkCampaignForm.valid) {
      validateAllFormFields(this.bulkCampaignForm);
      return;
    }
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.sameStatusRows = this.selectedNodes?.map((node: any) => node.data);
    let resource: any = {
      ids: this.sameStatusRows.map((item: Lead) => item.id),
    };
    let selectedCampaign = this.bulkCampaignForm.get('campaign').value;

    resource = {
      ...resource,
      campaignNames: selectedCampaign,
      shouldRemoveExistingCampaign: this.bulkCampaignForm?.get(
        'shouldRemoveExistingCampaign'
      )?.value,
    };
    this._store.dispatch(new BulkCampaign(resource));
    const intervalId = setInterval(() => {
      if (this.modalService.getModalsCount() === 0) {
        const numberOfLeads = resource?.ids?.length;
        if (numberOfLeads >= 50) {
          this.type = 'Bulk Campaign';
          this.modalRef = this.modalService.show(
            this.trackerInfoModal,
            Object.assign(
              {},
              {
                class: 'modal-400 top-modal ph-modal-unset',
                ignoreBackdropClick: true,
                keyboard: false,
              }
            )
          );
        }
      }
      clearInterval(intervalId);
    }, 2000);
    this.bulkCampaignIsLoading = true;
    this._store
      .select(getBulkCampaignIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.bulkCampaignIsLoading = isLoading;
        if (!isLoading) {
          this.bulkCampaignModalRef.hide();
        }
      });
    this.selectedNodes = {};
    this.sameStatusRows = [];
    this.bulkCampaignForm.reset();
    if (this.bulkCampaignModalRef) {
      this.bulkCampaignModalRef.hide();
      this.gridApi.deselectAll();
    }
  }

  updateBulkAgency() {
    if (!this.bulkAgencyFrom.valid) {
      validateAllFormFields(this.bulkAgencyFrom);
      return;
    }
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.sameStatusRows = this.selectedNodes?.map((node: any) => node.data);
    let resource: any = {
      ids: this.sameStatusRows.map((item: Lead) => item.id),
    };
    let selectedAgency = this.bulkAgencyFrom.get('agency').value;

    resource = {
      ...resource,
      agencyNames: selectedAgency,
      shouldRemoveExistingAgency: this.bulkAgencyFrom?.get(
        'shouldRemoveExistingAgency'
      )?.value,
    };
    this._store.dispatch(new BulkAgency(resource));
    const intervalId = setInterval(() => {
      if (this.modalService.getModalsCount() === 0) {
        const numberOfLeads = resource?.ids?.length;
        if (numberOfLeads >= 50) {
          this.type = 'Bulk Agency';
          this.modalRef = this.modalService.show(
            this.trackerInfoModal,
            Object.assign(
              {},
              {
                class: 'modal-400 top-modal ph-modal-unset',
                ignoreBackdropClick: true,
                keyboard: false,
              }
            )
          );
        }
      }
      clearInterval(intervalId);
    }, 2000);
    this.bulkAgencyIsLoading = true;
    this._store
      .select(getBulkAgencyIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.bulkAgencyIsLoading = isLoading;
        if (!isLoading) {
          this.bulkAgencyModalRef.hide();
        }
      });
    this.selectedNodes = {};
    this.sameStatusRows = [];
    this.bulkAgencyFrom.reset();
    if (this.bulkAgencyModalRef) {
      this.bulkAgencyModalRef.hide();
      this.gridApi.deselectAll();
    }
  }

  openBulkProjectModal(BulkProjectModal: TemplateRef<any>) {
    this.bulkProjectFrom = this.formBuilder.group({
      project: [null],
      ShouldRemoveExistingProjects: [false],
    });
    this._store.dispatch(new FetchProjectList());
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.selectedProjects = this.selectedNodes?.map((node: any) => node.data);
    let initialState: any = {
      data: Object.values(this.selectedProjects),
      class: 'right-modal modal-500 ip-modal-unset',
    };
    this.bulkProjectModalRef = this.modalService.show(
      BulkProjectModal,
      initialState
    );
    this.trackingService.trackFeature('Web.Leads.Button.BulkProject.Click');
  }

  updateBulkProjects() {
    if (!this.bulkProjectFrom.valid) {
      validateAllFormFields(this.bulkProjectFrom);
      return;
    }
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.sameStatusRows = this.selectedNodes?.map((node: any) => node.data);
    let resource: any = {
      ids: this.sameStatusRows.map((item: Lead) => item.id),
    };
    let selectedProject = this.bulkProjectFrom.get('project').value;

    resource = {
      ...resource,
      projectNames: selectedProject,
      ShouldRemoveExistingProjects: this.bulkProjectFrom?.get(
        'ShouldRemoveExistingProjects'
      )?.value,
    };
    this._store.dispatch(new BulkProjects(resource));
    const intervalId = setInterval(() => {
      if (this.modalService.getModalsCount() === 0) {
        const numberOfLeads = resource?.ids?.length;
        if (numberOfLeads >= 50) {
          this.type = 'Bulk Project';
          this.modalRef = this.modalService.show(
            this.trackerInfoModal,
            Object.assign(
              {},
              {
                class: 'modal-400 top-modal ph-modal-unset',
                ignoreBackdropClick: true,
                keyboard: false,
              }
            )
          );
        }
      }
      clearInterval(intervalId);
    }, 2000);
    this.bulkProjectsIsLoading = true;
    this._store
      .select(getBulkProjectIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.bulkProjectsIsLoading = isLoading;
        if (!isLoading) {
          this.bulkProjectModalRef.hide();
        }
      });
    this.selectedNodes = {};
    this.sameStatusRows = [];
    this.bulkProjectFrom.reset();
    if (this.bulkProjectModalRef) {
      this.bulkProjectModalRef.hide();
      this.gridApi.deselectAll();
    }
  }

  openBulkDeleteModal(BulkDeleteModal: TemplateRef<any>) {
    this.selectedLeads = this.gridApi?.getSelectedNodes().map((lead: any) => {
      return lead.data;
    });
    let initialState: any = {
      data: this.selectedLeads,
      class: 'right-modal modal-300',
    };
    this.bulkDeleteModalRef = this.modalService.show(
      BulkDeleteModal,
      initialState
    );
    this.trackingService.trackFeature('Web.Leads.Button.BulkDelete.Click');
  }

  getProjectNames(lead: any): string {
    return lead.projects.map((p: { name: any }) => p.name).join(', ');
  }

  getAgencyNames(lead: any): string {
    return lead.agencies.map((p: { name: any }) => p.name).join(', ');
  }

  getChannelPartnerNames(lead: any): string {
    return lead.channelPartners.map((p: { name: any }) => p.name).join(', ');
  }

  getCampaignNames(lead: any): string {
    return lead.campaigns.map((p: { name: any }) => p.name).join(', ');
  }

  updateBulkDelete() {
    let ids: any = [];
    this.selectedLeads?.map((lead: any) => ids.push(lead.id));
    if (
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] ===
      'Deleted' &&
      this.bulkPermanentDelete
    ) {
      this._store.dispatch(new RestoreLeads(ids));
      this._store
        .select(getBulkRestoreLeadsIsLoading)
        .pipe(
          skipWhile((isLoading: boolean) => isLoading),
          take(1)
        )
        .subscribe((isLoading: boolean) => {
          this.bulkRestoreIsLoading = isLoading;
          this.bulkDeleteModalRef.hide();
        });
    } else if (
      this.leadVisibilityEnum[this.appliedFilter?.leadVisibility] ===
      'Deleted' &&
      !this.bulkPermanentDelete
    ) {
      this.permanentDelete(ids);
      this.bulkDeleteModalRef.hide();
    } else {
      this.bulkDeleteIsLoading = true;
      this._store.dispatch(
        new DeleteLeads(ids, this.currentPath.includes('invoice'))
      );
      this._store
        .select(getBulkDeleteLeadsIsLoading)
        .pipe(
          skipWhile((isLoading: boolean) => isLoading),
          take(1)
        )
        .subscribe((isLoading: boolean) => {
          this.bulkDeleteIsLoading = isLoading;
          this.bulkDeleteModalRef.hide();
        });
    }
  }

  openBulkEmailPopup() {
    this.trackingService.trackFeature('Web.Leads.Button.BulkEmail.Click');
    this.modalRef?.hide();
    this.selectedNodes = this.gridApi?.getSelectedNodes();
    let initialState: any = {
      data: {
        bulkData: this.selectedNodes?.map((data: any) => data?.data),
        shareType: 'Email',
        selectedNodes: this.selectedNodes,
      },
    };

    this.modalRef = this.modalService.show(
      BulkLeadsEmailShareComponent,
      Object.assign(
        {},
        { class: 'modal-600 right-modal ip-modal-unset', initialState }
      )
    );
  }

  openEmailConfirmation(changePopup: any, noMail: any) {
    this.selectedNodes = this.gridApi?.getSelectedNodes();
    const emails: string[] = this.selectedNodes?.map((node: any) =>
      node?.data?.email ? node?.data?.email : null
    );
    const noOfEmails: number = emails?.filter((email: string) => email)?.length;
    if (emails?.length == noOfEmails) {
      this.openBulkEmailPopup();
      return;
    }

    if (noOfEmails === 0) {
      this.message = `No emails were attached in the selected leads`;
      this.modalRef = this.modalService.show(noMail, {
        class: 'modal-600 top-modal ip-modal-unset',
      });
      return;
    }

    this.modalRef = this.modalService.show(changePopup, {
      class: 'modal-600 top-modal ip-modal-unset',
    });

    this.message = `You have selected ${emails?.length} Leads, only ${noOfEmails} of them have emails attached. Do you want to proceed?`;
    this.trackingService.trackFeature(
      'Web.Leads.Page.NoEmailAttachedPopup.Visit'
    );
  }

  openBulkShareModal() {
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    const selectedLeads = this.selectedNodes?.map((node: any) => node.data);
    let initialState: any = {
      data: Object.values(selectedLeads),
      selectedNodes: this.selectedNodes,
    };
    // if(this.globalSettingsData?.isWhatsAppDeepIntegration) {
    // this.modalService.show(
    //   WhatsappChatBulkComponent,
    //   Object.assign(
    //     {},
    //     {
    //       class: 'right-modal modal-450 ph-modal-unset',
    //       initialState,
    //     }
    //   )
    // );
    // } else {
    this.modalService.show(
      LeadBulkShareComponent,
      Object.assign(
        {},
        {
          class: 'right-modal modal-300',
          initialState,
        }
      )
    );
    this.trackingService.trackFeature('Web.Leads.Button.BulkWhatsApp.Click');
    // }
  }

  openConfirmDeleteModal(leadName: string, leadId: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: leadName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeLead(leadId);
        }
      });
    }
  }

  onSourceChange(): void {
    this.subSourceList = [];
    const selectedSource = this.bulkReassignForm?.get('selectedSource')?.value;
    if (selectedSource) {
      this.bulkReassignForm?.get('selectedSubSource')?.setValue(null);
      if (selectedSource.displayName === '99 Acres') {
        this.subSourceList = this.allSubSourceList['NinetyNineAcres'] || [];
      } else if (selectedSource.displayName === 'Roof&Floor') {
        this.subSourceList = this.allSubSourceList['RoofandFloor'] || [];
      } else {
        const formattedSourceName = selectedSource.displayName.replace(/\s+/g, '');
        if (this.allSubSourceList[formattedSourceName]) {
          this.subSourceList = this.allSubSourceList[formattedSourceName] || [];
        } else {
          this.subSourceList = this.allSubSourceList[selectedSource.displayName] || [];
        }
      }
    } else {
      this.bulkReassignForm?.get('selectedSource')?.setValue(null);
      this.bulkReassignForm?.get('selectedSubSource')?.setValue(null);
    }
  }

  updateSubSourceForBulkSource() {
    this.subSourceList = [];
    if (this.bulkSourceForm?.get('source')?.value) {
      const selectedSource = this.bulkSourceForm?.get('source').value;
      const sourceName = selectedSource.displayName;
      if (sourceName === '99Acres') {
        this.subSourceList.push.apply(
          this.subSourceList,
          this.allSubSourceList['NinetyNineAcres'] || []
        );
      } else if (sourceName === 'Roof&Floor') {
        this.subSourceList.push.apply(
          this.subSourceList,
          this.allSubSourceList['RoofandFloor'] || []
        );
      } else {
        const formattedSourceName = sourceName.replace(/\s+/g, '');
        if (this.allSubSourceList[formattedSourceName]) {
          this.subSourceList.push.apply(
            this.subSourceList,
            this.allSubSourceList[formattedSourceName] || []
          );
        } else {
          this.subSourceList.push.apply(
            this.subSourceList,
            this.allSubSourceList[sourceName] || []
          );
        }
      }
      this.bulkSourceForm?.get('subsource')?.setValue(null);
    } else {
      this.bulkSourceForm?.get('source')?.setValue(null);
      this.bulkSourceForm?.get('subsource')?.setValue(null);
    }
  }

  removeLead(id: string): void {
    const selectedNode = this.gridApi
      ?.getSelectedNodes()
      ?.find((lead: any) => lead?.data?.id === id);
    if (selectedNode) {
      this.gridApi?.deselectNode(selectedNode);
    }

    const filters = [
      'selectedBulkReassign',
      'selectedSources',
      'selectedProjects',
      'selectedAgencies',
      'selectedLeads',
    ] as const;
    filters.forEach((filter) => {
      const filterArray = this[filter] as any[];
      if (filterArray) {
        this[filter] = filterArray.filter((lead: any) => lead?.id !== id);
      }
    });

    if (filters.every((filter) => !(this[filter] as any[])?.length)) {
      this.bulkReassignModalRef.hide();
      this.bulkSourceModalRef.hide();
      this.bulkSecondaryReassignModalRef.hide();
      this.bulkUpdateModalRef.hide();
      this.bulkProjectModalRef.hide();
      this.bulkDeleteModalRef.hide();
    }
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.firstName} ${user.lastName}`;
    });
    return userName;
  }

  assignToName(lead: any): string {
    return getAssignedToDetails(lead?.assignTo, this.allUsers, true) || '';
  }

  sortColumn(column: string, isInAscOrder: boolean): void {
    if (column == 'name') {
      this.selectedBulkReassign.sort((lead1: any, lead2: any) =>
        isInAscOrder
          ? lead2.name.localeCompare(lead1.name)
          : lead1.name.localeCompare(lead2.name)
      );
      this.isLeadNameColInAscOrder = !isInAscOrder;
      return;
    }

    this.selectedBulkReassign.sort((lead1: any, lead2: any) =>
      isInAscOrder
        ? this.assignToName(lead2).localeCompare(this.assignToName(lead1))
        : this.assignToName(lead1).localeCompare(this.assignToName(lead2))
    );
    this.isAssignToColInAscOrder = !isInAscOrder;
  }

  secondaryAssignToName(lead: any): string {
    return (
      getAssignedToDetails(lead?.secondaryUserId, this.allUsers, true) || ''
    );
  }

  assignToUserListChanged(): void {
    if (
      this.bulkReassignForm
        .get('assignedToUsers')
        .value.some((user: any) => user.value === '')
    ) {
      const assignedToUsers = this.bulkReassignForm
        .get('assignedToUsers')
        .value.filter((user: any) => user.value === '');
      this.bulkReassignForm.patchValue({
        assignedToUsers: assignedToUsers,
        assignmentType: LeadAssignmentType.WithHistory,
        isChangeSourceSelected: false,
        selectedSource: null,
        selectedSubSource: null,
        isChangeProjectSelected: false,
        selectedProject: null,
        isCreateDuplicateSelected: false,
      });
      this.isUnassignLeadSelected = true;
      return;
    }
    this.isUnassignLeadSelected = false;
    this.trackingService.trackFeature('Web.Leads.Options.AssignToView.Click');
  }

  permanentDelete(data: any) {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      type: 'permanentDelete',
      data: {
        fieldType: 'Delete',
        heading: `Delete ${data?.length} lead(s) permanently?`,
        message: `You are about to delete the ${data?.length} lead(s) permanently. 🚫`,
        description:
          'Delete all information associated with these lead(s). Once the lead(s) are deleted, the action is irreversible, and data recovery will not be possible.',
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-450 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this._store.dispatch(new PermanentDeleteLeads(data));
        }
      });
    }
  }

  onAssignmentTypeChange(selectedValue: string) {
    let eventName = '';
    switch (selectedValue) {
      case 'With history':
        eventName = 'Web.Leads.Button.withHistory.Click';
        break;
      case 'Without history':
        eventName = 'Web.Leads.Button.withoutHistory.Click';
        break;
      case 'Without history and new status':
        eventName = 'Web.Leads.Button.withouthistorynewstatus.Click';
        break;
    }
    if (eventName) {
      this.trackingService.trackFeature(eventName);
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}

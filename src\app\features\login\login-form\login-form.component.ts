import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AnimationOptions } from 'ngx-lottie';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { SUPP0RT_NO } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  getEnvDetails,
  getTenantName,
  isEmptyObject,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { Login, LoginSuccess } from 'src/app/reducers/login/login.actions';
import { getIsLoginLoading, getLogin } from 'src/app/reducers/login/login.reducer';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'login-form',
  templateUrl: './login-form.component.html',
})
export class LoginFormComponent implements OnInit, OnDestroy {
  loginForm: FormGroup;
  supportNumber = SUPP0RT_NO;
  subDomain: string = getTenantName();
  isShowLogin: boolean = true;
  isShowSupport: boolean = false;
  isShowPassword: boolean = false;
  isLoginLoading: boolean = false;
  currentYear: number = new Date().getFullYear();
  getEnvDetails = getEnvDetails;
  isAccountLocked: boolean = false;
  lockoutTimer: any;
  lockoutTimeRemaining: number = 300;
  lockoutMinutes: number = 0;
  lockoutSeconds: number = 0;
  private destroy$ = new Subject<void>();
  building: AnimationOptions = {
    path: 'assets/animations/building.json',
  };
  online: AnimationOptions = {
    path: 'assets/animations/circle-green.json',
  };
  frontLaptop: AnimationOptions = {
    path: 'assets/animations/front-laptop.json',
  };

  constructor(
    private fb: FormBuilder,
    private store: Store<AppState>,
    public metaTitle: Title,
    public router: Router,
    public trackingService: TrackingService,
    private cdr: ChangeDetectorRef
  ) {
    this.metaTitle.setTitle('CRM | Login');
    this.loginForm = this.fb.group({
      username: ['', Validators.required],
      password: ['', Validators.required],
    });
    this.trackingService?.trackFeature('Web.Login.Page.Login.Visit');
  }

  ngOnInit() {
    this.setupLoginSubscriptions();
  }

  login() {
    if (!this.loginForm.valid) {
      validateAllFormFields(this.loginForm);
      return;
    }
    const payload = { ...this.loginForm.value };
    this.isLoginLoading = true;
    this.store.dispatch(new LoginSuccess({}));
    this.store.dispatch(new Login(payload));
  }

  setupLoginSubscriptions() {
    this.store.select(getIsLoginLoading)
      .pipe(takeUntil(this.destroy$))
      .subscribe((isLoading: boolean) => {
        if (!isLoading) {
          this.isLoginLoading = isLoading;
        }
      });

    this.store.select(getLogin)
      .pipe(takeUntil(this.destroy$))
      .subscribe((data: any) => {
        if (!isEmptyObject(data) && !data?.ok) {
          this.isLoginLoading = false;
          const errorMessage = data?.error?.messages?.[0];
          if (errorMessage) {
            this.checkForLockoutMessage(errorMessage);
          }
        }
      });
  }

  navigateToForgotPassword() {
    this.router.navigate(['login/forgot-password'], {
      state: { userName: this.loginForm.value?.username },
    });
  }

  startLockoutTimer(timeInSeconds?: number) {
    this.isAccountLocked = true;
    this.lockoutTimeRemaining = timeInSeconds || 300;
    this.updateTimerDisplay();
    this.cdr.detectChanges();
    this.startTimer();
  }

  startTimer() {
    if (this.lockoutTimer) {
      clearInterval(this.lockoutTimer);
    }
    this.lockoutTimer = setInterval(() => {
      this.lockoutTimeRemaining--;
      this.updateTimerDisplay();
      if (this.lockoutTimeRemaining <= 0) {
        this.clearLockoutTimer();
      }
    }, 1000);
  }

  updateTimerDisplay() {
    this.lockoutMinutes = Math.floor(this.lockoutTimeRemaining / 60);
    this.lockoutSeconds = this.lockoutTimeRemaining % 60;
  }

  clearLockoutTimer() {
    this.isAccountLocked = false;
    this.lockoutTimeRemaining = 300;
    this.lockoutMinutes = 0;
    this.lockoutSeconds = 0;

    if (this.lockoutTimer) {
      clearInterval(this.lockoutTimer);
      this.lockoutTimer = null;
    }
  }

  checkForLockoutMessage(errorMessage: string) {
    if (errorMessage && (errorMessage.includes('Account has been locked') || errorMessage.includes('Account is locked'))) {
      const timeInSeconds = this.parseTimeFromMessage(errorMessage);
      this.startLockoutTimer(timeInSeconds);
    }
  }

  parseTimeFromMessage(message: string): number {
    let totalSeconds = 300;
    const minutesMatch = message.match(/(\d+)\s*minutes?/i);
    const secondsMatch = message.match(/(\d+)\s*sec/i);
    if (minutesMatch || secondsMatch) {
      const minutes = minutesMatch ? parseInt(minutesMatch[1], 10) : 0;
      const seconds = secondsMatch ? parseInt(secondsMatch[1], 10) : 0;
      totalSeconds = (minutes * 60) + seconds;
    }
    return totalSeconds;
  }



  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();

    if (this.lockoutTimer) {
      clearInterval(this.lockoutTimer);
    }
  }
}

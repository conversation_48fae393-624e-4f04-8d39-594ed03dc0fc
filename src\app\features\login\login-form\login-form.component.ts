import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AnimationOptions } from 'ngx-lottie';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { SUPP0RT_NO } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  getEnvDetails,
  getTenantName,
  isEmptyObject,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { Login, LoginSuccess } from 'src/app/reducers/login/login.actions';
import { getIsLoginLoading, getLogin } from 'src/app/reducers/login/login.reducer';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'login-form',
  templateUrl: './login-form.component.html',
})
export class LoginFormComponent implements OnInit, OnDestroy {
  loginForm: FormGroup;
  supportNumber = SUPP0RT_NO;
  subDomain: string = getTenantName();
  isShowLogin: boolean = true;
  isShowSupport: boolean = false;
  isShowPassword: boolean = false;
  isLoginLoading: boolean = false;
  currentYear: number = new Date().getFullYear();
  getEnvDetails = getEnvDetails;
  isAccountLocked: boolean = false;
  lockoutTimer: any;
  lockoutTimeRemaining: number = 300;
  lockoutMinutes: number = 0;
  lockoutSeconds: number = 0;
  isPasswordResetRequired: boolean = false;
  private destroy$ = new Subject<void>();
  building: AnimationOptions = {
    path: 'assets/animations/building.json',
  };
  online: AnimationOptions = {
    path: 'assets/animations/circle-green.json',
  };
  frontLaptop: AnimationOptions = {
    path: 'assets/animations/front-laptop.json',
  };

  constructor(
    private fb: FormBuilder,
    private store: Store<AppState>,
    public metaTitle: Title,
    public router: Router,
    public trackingService: TrackingService,
    private cdr: ChangeDetectorRef
  ) {
    this.metaTitle.setTitle('CRM | Login');
    this.loginForm = this.fb.group({
      username: ['', Validators.required],
      password: ['', Validators.required],
    });
    this.trackingService?.trackFeature('Web.Login.Page.Login.Visit');
  }

  ngOnInit() {
    this.setupLoginSubscriptions();
    this.checkPasswordResetReturn();
    this.setupWindowFocusListener();
  }

  login() {
    if (!this.loginForm.valid) {
      validateAllFormFields(this.loginForm);
      return;
    }
    const payload = { ...this.loginForm.value };
    this.isLoginLoading = true;
    this.store.dispatch(new LoginSuccess({}));
    this.store.dispatch(new Login(payload));
  }

  setupLoginSubscriptions() {
    this.store.select(getIsLoginLoading)
      .pipe(takeUntil(this.destroy$))
      .subscribe((isLoading: boolean) => {
        if (!isLoading) {
          this.isLoginLoading = isLoading;
        }
      });

    this.store.select(getLogin)
      .pipe(takeUntil(this.destroy$))
      .subscribe((data: any) => {
        if (!isEmptyObject(data)) {
          if (data?.ok) {
            // Login successful - clear any active timers
            this.clearLockoutTimer();
          } else {
            // Login failed - handle error messages
            this.isLoginLoading = false;
            const errorMessage = data?.error?.messages?.[0];
            if (errorMessage) {
              this.checkForLockoutMessage(errorMessage);
            }
          }
        }
      });
  }

  navigateToForgotPassword() {
    // Store current lockout state before navigation
    const wasLocked = this.isAccountLocked;
    const wasPasswordResetRequired = this.isPasswordResetRequired;

    // Set flag to indicate user is going to reset password
    sessionStorage.setItem('userInitiatedPasswordReset', 'true');

    this.router.navigate(['login/forgot-password'], {
      state: {
        userName: this.loginForm.value?.username,
        wasLocked: wasLocked,
        wasPasswordResetRequired: wasPasswordResetRequired
      },
    });
  }

  checkPasswordResetReturn() {
    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras?.state || history.state;

    // Check if user initiated password reset from this login page
    const userInitiatedReset = sessionStorage.getItem('userInitiatedPasswordReset');

    if (state?.passwordResetCompleted || state?.fromPasswordReset || userInitiatedReset) {
      // Clear the flag
      sessionStorage.removeItem('userInitiatedPasswordReset');
      // Clear any active lockout timers and states
      this.clearLockoutTimer();
    }
  }

  onPasswordFieldChange() {
    if (this.isPasswordResetRequired) {
      this.isPasswordResetRequired = false;
    }
  }

  setupWindowFocusListener() {
    // Listen for when user returns to this tab/window (e.g., after password reset)
    window.addEventListener('focus', () => {
      this.checkPasswordResetReturn();
    });

    // Also listen for visibility change
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.checkPasswordResetReturn();
      }
    });
  }

  startLockoutTimer(timeInSeconds?: number) {
    this.isAccountLocked = true;
    this.lockoutTimeRemaining = timeInSeconds || 300;
    this.updateTimerDisplay();
    this.cdr.detectChanges();
    this.startTimer();
  }

  startTimer() {
    if (this.lockoutTimer) {
      clearInterval(this.lockoutTimer);
    }
    this.lockoutTimer = setInterval(() => {
      this.lockoutTimeRemaining--;
      this.updateTimerDisplay();
      if (this.lockoutTimeRemaining <= 0) {
        this.clearLockoutTimer();
      }
    }, 1000);
  }

  updateTimerDisplay() {
    this.lockoutMinutes = Math.floor(this.lockoutTimeRemaining / 60);
    this.lockoutSeconds = this.lockoutTimeRemaining % 60;
  }

  clearLockoutTimer() {
    this.isAccountLocked = false;
    this.isPasswordResetRequired = false;
    this.lockoutTimeRemaining = 300;
    this.lockoutMinutes = 0;
    this.lockoutSeconds = 0;

    if (this.lockoutTimer) {
      clearInterval(this.lockoutTimer);
      this.lockoutTimer = null;
    }
  }

  checkForLockoutMessage(errorMessage: string) {
    this.isPasswordResetRequired = false;
    this.isAccountLocked = false;

    if (errorMessage && errorMessage.toLowerCase().includes('please reset your password')) {
      this.isPasswordResetRequired = true;
    } else if (errorMessage && (errorMessage.includes('Account has been locked') || errorMessage.includes('Account is locked'))) {
      const timeInSeconds = this.parseTimeFromMessage(errorMessage);
      this.startLockoutTimer(timeInSeconds);
    }
  }

  parseTimeFromMessage(message: string): number {
    let totalSeconds = 300;
    const minutesMatch = message.match(/(\d+)\s*minutes?/i);
    const secondsMatch = message.match(/(\d+)\s*sec/i);
    if (minutesMatch || secondsMatch) {
      const minutes = minutesMatch ? parseInt(minutesMatch[1], 10) : 0;
      const seconds = secondsMatch ? parseInt(secondsMatch[1], 10) : 0;
      totalSeconds = (minutes * 60) + seconds;
    }
    return totalSeconds;
  }



  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();

    if (this.lockoutTimer) {
      clearInterval(this.lockoutTimer);
    }
  }
}
